import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:stockhero/models/bot_config.dart';
import 'package:stockhero/shared/controllers/auth_controller.dart';
import 'package:stockhero/shared/providers/bot_config_provider.dart';
import 'package:stockhero/shared/utils/theme_reflector.dart';
import 'package:stockhero/shared/utils/theme_utils.dart';
import 'package:stockhero/utils/constants.dart';

class ExitScreen extends StatefulWidget {
  const ExitScreen({Key? key}) : super(key: key);

  @override
  State<ExitScreen> createState() => _ExitScreenState();
}

class _ExitScreenState extends State<ExitScreen> {
  // Take profit and stop loss percentages
  double takeProfit = 0.1;
  double stopLoss = 0.1;

  // Indicator triggers
  List<Map<String, dynamic>> indicators = [];

  // Minimum indicators required
  double minIndicators = 0.0;

  // API data for indicators
  List<Map<String, dynamic>> availableIndicators = [];
  bool isLoadingIndicators = false;

  // Bot data
  bool isLoadingBotData = false;
  String? errorMessage;
  int? botId;
  bool isEditing = false;
  Map<String, dynamic> fullBotData = {};
  String? botType;

  // Price bot specific controller
  final TextEditingController _sellPriceController = TextEditingController();

  // Sell bot specific controllers
  final TextEditingController _sellPriceForSellBotController =
      TextEditingController();
  final TextEditingController _sellPriceLowerController =
      TextEditingController();
  final TextEditingController _minProfitController = TextEditingController();

  // Sell bot specific variables
  bool sellBotTradingViewEnabled = false;

  // Grid Exit specific variables
  double gridWaitTime = 10.0;
  String gridOuterRangeLimit = '2'; // Default 2%
  int selectedScenario1Option = 0; // First scenario selection
  int selectedScenario2Option = 0; // Second scenario selection

  // Momentum Exit specific variables
  double takeProfitPercentage = 0.0;
  double stopLossPercentage = 0.0;
  bool tradingViewEnabled = false;
  double indicatorTriggers = 0.0;
  double minimumProfitForTrigger = 0.0;
  bool disableBotOnStopLoss = false;
  int stopLossRowCount = 0; // Số lần stop loss liên tiếp
  bool autoClosePositions = false;
  bool stopAfterCurrentDeal = false;
  bool autoCloseAtEndOfDay = false;

  // Advanced bot specific variables
  String selectedTakeProfitType = 'Absolute Take Profit'; // Default option
  String selectedStopLossType = 'Absolute'; // Default option

  // Fixed Trailing Take Profit variables
  final TextEditingController _fixedTrailingSellPercentController =
      TextEditingController();
  final TextEditingController _fixedTrailingPricePercentController =
      TextEditingController();

  // Variable Trailing Take Profit variables
  List<Map<String, TextEditingController>> variableTrailingControllers = [
    {
      'sellPercent': TextEditingController(),
      'pricePercent': TextEditingController(),
    }
  ];

  @override
  void initState() {
    super.initState();

    // Đảm bảo không có giá trị nào = 0
    if (takeProfit == 0) takeProfit = 0.1;
    if (stopLoss == 0) stopLoss = 0.1;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _processRouteArguments();
      _fetchIndicators();

      // Load indicators từ BotConfigProvider khi không phải editing
      if (!isEditing) {
        _loadIndicatorsFromProvider();
      }

      // Kiểm tra lại giá trị sau khi xử lý tham số
      Future.delayed(const Duration(milliseconds: 500), () {
        _ensureValidSliderValues();
      });
    });
  }

  void _processRouteArguments() {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null) {
      setState(() {
        botId = args['botId'];
        isEditing = args['isEditing'] == true;
        botType = args['botType']?.toString();
      });

      if (botId != null && isEditing) {
        _fetchBotDetails(botId!);
      }
    }
  }

  Future<void> _fetchBotDetails(int id) async {
    setState(() {
      isLoadingBotData = true;
      errorMessage = null;
    });

    try {
      final authController =
          Provider.of<AuthController>(context, listen: false);
      final token = authController.token;

      if (token == null) {
        setState(() {
          errorMessage = 'Authentication error. Please log in again.';
          isLoadingBotData = false;
        });
        return;
      }

      final response = await http.get(
        Uri.parse(
            '${AppConstants.apiBaseUrl}${AppConstants.getBotByIdEndpoint}$id'),
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.apiTimeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        print('API Response for bot ID $id');
        print(
            'profit: ${data['bot']['profit']}, stop_loss: ${data['bot']['stop_loss']}');

        if (data['code'] == 200 && data['bot'] != null) {
          final botData = data['bot'] as Map<String, dynamic>;

          // Save full bot data
          fullBotData = botData;

          setState(() {
            // Lấy giá trị take profit trực tiếp từ API
            if (botData['profit'] != null) {
              if (botData['profit'] is num) {
                takeProfit = (botData['profit'] as num).toDouble();
              } else if (botData['profit'] is String) {
                takeProfit = double.parse(botData['profit']);
              }
              // Đảm bảo giá trị hợp lệ
              if (takeProfit < 0.1) takeProfit = 0.1;
              print('Successfully set takeProfit to: $takeProfit');
            }

            // Lấy giá trị stop loss trực tiếp từ API
            if (botData['stop_loss'] != null) {
              if (botData['stop_loss'] is num) {
                stopLoss = (botData['stop_loss'] as num).toDouble();
              } else if (botData['stop_loss'] is String) {
                stopLoss = double.parse(botData['stop_loss']);
              }
              // Đảm bảo giá trị hợp lệ
              if (stopLoss < 0.1) stopLoss = 0.1;
              print('Successfully set stopLoss to: $stopLoss');
            }

            // Lấy giá trị indicator triggers
            if (botData['indicator_triggers_exit'] != null) {
              if (botData['indicator_triggers_exit'] is num) {
                minIndicators =
                    (botData['indicator_triggers_exit'] as num).toDouble();
              } else if (botData['indicator_triggers_exit'] is String) {
                minIndicators =
                    double.parse(botData['indicator_triggers_exit']);
              }
            }

            // Lấy danh sách indicators
            if (botData['bot_indicators'] != null) {
              final botIndicators = botData['bot_indicators'] as List;
              final exitIndicators = botIndicators
                  .where((ind) => ind['type'] == 'exit')
                  .map((e) => e as Map<String, dynamic>)
                  .toList();

              if (exitIndicators.isNotEmpty) {
                indicators = exitIndicators;
              }
            }

            isLoadingBotData = false;
          });

          print('Bot exit details loaded successfully');
          print('Final takeProfit: $takeProfit');
          print('Final stopLoss: $stopLoss');
        } else {
          setState(() {
            errorMessage = data['message'] ?? 'Failed to load bot details';
            isLoadingBotData = false;
          });
        }
      } else {
        print('API error status code: ${response.statusCode}');
        print('API error response: ${response.body}');
        setState(() {
          errorMessage =
              'Failed to load bot details. Status code: ${response.statusCode}';
          isLoadingBotData = false;
        });
      }
    } catch (e) {
      print('Exception details: $e');
      setState(() {
        errorMessage = 'Error loading bot details: $e';
        isLoadingBotData = false;
      });
      print('Exception in _fetchBotDetails: $e');
    }
  }

  // Load indicators từ BotConfigProvider (giống entry screen)
  void _loadIndicatorsFromProvider() {
    try {
      final botConfigProvider =
          Provider.of<BotConfigProvider>(context, listen: false);
      final config = botConfigProvider.botConfig;

      setState(() {
        // Load existing exit indicators
        indicators.clear();

        if (config.exits.exitIndicators.isNotEmpty) {
          // Load existing indicators
          for (String indicatorStr in config.exits.exitIndicators) {
            // Parse indicator string like "RSI (14, 30, 70)"
            Map<String, dynamic> indicator =
                _parseIndicatorString(indicatorStr);
            indicators.add(indicator);
          }
        } else if (botType == 'sell') {
          // Sell bot không có default indicators, để user tự chọn
          indicators = [];
          print('Sell bot: No default indicators, user will add manually');
        }

        // Parse minimum indicators required
        if (config.exits.minIndicatorsRequired.isNotEmpty) {
          final regex = RegExp(r'(\d+)\s+out\s+of\s+(\d+)');
          final match = regex.firstMatch(config.exits.minIndicatorsRequired);
          if (match != null) {
            minIndicators = double.tryParse(match.group(1) ?? '0') ?? 0.0;
          }
        }
        // Không tự động set minIndicators, để user tự chọn

        // Load grid exit settings if available
        if (config.exits.gridWaitTime != null) {
          gridWaitTime = config.exits.gridWaitTime!;
        }
        if (config.exits.gridOuterRangeLimit != null) {
          gridOuterRangeLimit = config.exits.gridOuterRangeLimit!;
        }
        if (config.exits.gridExitStrategyNoTrade != null) {
          selectedScenario1Option = config.exits.gridExitStrategyNoTrade!;
        }
        if (config.exits.gridExitStrategyHasTrade != null) {
          selectedScenario2Option = config.exits.gridExitStrategyHasTrade!;
        }
      });

      print('Loaded existing exit data from BotConfigProvider:');
      print('Loaded ${indicators.length} exit indicators');
      print('Min Indicators: $minIndicators');
    } catch (e) {
      print('Error loading indicators from provider: $e');
    }
  }

  // Parse indicator string với đầy đủ fields theo format ví dụ
  Map<String, dynamic> _parseIndicatorString(String indicatorStr) {
    // Parse indicator string like "RSI (14, 30, 70)"
    final parts = indicatorStr.split('(');
    final name = parts[0].trim();

    // Base indicator với đầy đủ fields theo format ví dụ bạn đưa
    Map<String, dynamic> indicator = {
      'id': null,
      'name': name,
      'description': null,
      'period_num': 14, // Default values
      'value2': 30,
      'value3': 70,
      'value4': null,
      'value5': null,
      'value6': null,
      'value7': null,
      'value8': null,
      'value9': null,
      'value10': null,
      'value11': null,
      'value12': null,
      'value13': null,
      'value14': null,
      'value15': null,
      'value16': null,
      'value17': null,
      'value18': null,
      'value19': null,
      'value20': null,
      'created_at': null,
      'updated_at': null,
      'ind_key': DateTime.now().millisecondsSinceEpoch, // Random key
      'indicator_id': 1, // Default indicator ID
      'type': 'exit'
    };

    if (parts.length > 1) {
      final paramStr = parts[1].replaceAll(')', '').trim();
      final params = paramStr.split(',').map((s) => s.trim()).toList();

      if (params.isNotEmpty && params[0].isNotEmpty) {
        indicator['period_num'] = int.tryParse(params[0]) ?? 14;
      }
      if (params.length > 1 && params[1].isNotEmpty) {
        indicator['value2'] = double.tryParse(params[1]) ?? 30;
      }
      if (params.length > 2 && params[2].isNotEmpty) {
        indicator['value3'] = double.tryParse(params[2]) ?? 70;
      }
    }

    return indicator;
  }

  Future<void> _fetchIndicators() async {
    setState(() {
      isLoadingIndicators = true;
    });

    try {
      final authController =
          Provider.of<AuthController>(context, listen: false);
      final token = authController.token;

      if (token == null) {
        print('Authentication token not found');
        setState(() {
          isLoadingIndicators = false;
        });
        return;
      }

      final response = await http.get(
        Uri.parse('${AppConstants.apiBaseUrl}/api/indicators'),
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.apiTimeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['code'] == 200 && data['indicators'] != null) {
          final indicatorsList = data['indicators'] as List;

          setState(() {
            availableIndicators =
                indicatorsList.map((e) => e as Map<String, dynamic>).toList();
            isLoadingIndicators = false;
          });

          print('Loaded ${availableIndicators.length} indicators');
        } else {
          print('Failed to load indicators: ${data['message']}');
          setState(() {
            isLoadingIndicators = false;
          });
        }
      } else {
        print('Failed to load indicators. Status code: ${response.statusCode}');
        setState(() {
          isLoadingIndicators = false;
        });
      }
    } catch (e) {
      print('Exception in _fetchIndicators: $e');
      setState(() {
        isLoadingIndicators = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Kiểm tra các giá trị không hợp lệ
    if (takeProfit < 0.1 || stopLoss < 0.1) {
      // Sửa lỗi và hiển thị thông báo
      WidgetsBinding.instance.addPostFrameCallback((_) {
        String errorMessage = '';
        if (takeProfit < 0.1) errorMessage += 'Take Profit < 0.1, ';
        if (stopLoss < 0.1) errorMessage += 'Stop Loss < 0.1, ';

        // Hiển thị thông báo
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(
        //     content: Text(
        //       'Phát hiện giá trị không hợp lệ: $errorMessageđã tự động điều chỉnh để tránh lỗi ứng dụng.',
        //     ),
        //     backgroundColor:
        //         ThemeReflector.statusColor(context, status: StatusType.warning),
        //     duration: const Duration(seconds: 5),
        //   ),
        // );

        // Tự động sửa giá trị
        setState(() {
          if (takeProfit < 0.1) takeProfit = 0.1;
          if (stopLoss < 0.1) stopLoss = 0.1;
        });
      });
    }

    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: ThemeReflector.screenBackground(context),
      appBar: AppBar(
        backgroundColor: ThemeReflector.surfaceColor(context),
        elevation: 0,
        title: Text(
          'Create Bot',
          style: TextStyle(
            color: ThemeReflector.textColor(
              context,
              importance: TextImportance.primary,
            ),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: ThemeReflector.iconColor(context),
            size: 24,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.close,
              color: ThemeReflector.iconColor(context),
              size: 24,
            ),
            onPressed: () => Navigator.pushNamedAndRemoveUntil(
              context,
              AppConstants.dashboardRoute,
              (route) => false,
            ),
          ),
        ],
      ),
      body: isLoadingBotData
          ? const Center(child: CircularProgressIndicator())
          : (errorMessage != null
              ? Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline,
                            color: ThemeReflector.statusColor(context,
                                status: StatusType.error),
                            size: 48),
                        const SizedBox(height: 16),
                        Text(
                          errorMessage!,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              color: ThemeReflector.statusColor(context,
                                  status: StatusType.error)),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () {
                            if (botId != null) {
                              _fetchBotDetails(botId!);
                            } else {
                              Navigator.of(context).pop();
                            }
                          },
                          child: const Text('Try Again'),
                        ),
                      ],
                    ),
                  ),
                )
              : Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            // Header with image and title
                            _buildHeader(theme, colorScheme, textTheme),

                            // Grid Exit UI
                            if (botType?.toLowerCase() == 'grid')
                              _buildGridExitSection(theme)
                            // Momentum Exit UI
                            else if (botType?.toLowerCase() == 'momentum')
                              _buildMomentumExitSection(theme)
                            // Quickstart Exit UI (simplified)
                            else if (botType?.toLowerCase() == 'quickstart')
                              _buildQuickstartExitSection(theme)
                            // Advanced Exit UI
                            else if (botType?.toLowerCase() == 'advanced')
                              _buildAdvancedExitSection(theme)
                            else ...[
                              // Price input for price bot type - chỉ hiển thị input field duy nhất
                              if (botType == 'price')
                                _buildSellPriceInputSection(theme)
                              else ...[
                                // Sell price inputs for sell bot type
                                if (botType == 'sell')
                                  _buildSellBotInputSections(theme),

                                // Hide Take Profit and Stop Loss for sell bot
                                if (botType != 'sell') ...[
                                  // Take Profit Slider
                                  _buildSection(
                                    theme: theme,
                                    title: 'Take Profit',
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        _buildSliderWithValue(
                                          theme: theme,
                                          value: takeProfit,
                                          onChanged: (value) {
                                            setState(() {
                                              takeProfit = value;
                                            });
                                          },
                                          displayValue:
                                              '${takeProfit.toStringAsFixed(1)}%',
                                          color: const Color(0xFF14B377),
                                          min: 0.1,
                                          max: 100,
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          'Note: Values above 100% may cause deployment errors',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: takeProfit > 100
                                                ? ThemeReflector.statusColor(
                                                    context,
                                                    status: StatusType.error)
                                                : ThemeReflector.textColor(
                                                    context,
                                                    importance: TextImportance
                                                        .secondary),
                                            fontStyle: FontStyle.italic,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  // Stop Loss Slider
                                  _buildSection(
                                    theme: theme,
                                    title: 'Stop Loss',
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        _buildSliderWithValue(
                                          theme: theme,
                                          value: stopLoss,
                                          onChanged: (value) {
                                            setState(() {
                                              stopLoss = value;
                                            });
                                          },
                                          displayValue:
                                              '${stopLoss.toStringAsFixed(1)}%',
                                          color: const Color(0xFFF16A6C),
                                          min: 0.1,
                                          max: 100,
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          'Note: Values above 100% may cause deployment errors',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: stopLoss > 100
                                                ? ThemeReflector.statusColor(
                                                    context,
                                                    status: StatusType.error)
                                                : ThemeReflector.textColor(
                                                    context,
                                                    importance: TextImportance
                                                        .secondary),
                                            fontStyle: FontStyle.italic,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  Divider(
                                      height: 1,
                                      color:
                                          ThemeReflector.dividerColor(context)),

                                  // Indicator Triggers
                                  Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Column(
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Row(
                                              children: [
                                                Text(
                                                  'Indicator Triggers for Exit',
                                                  style: TextStyle(
                                                    color: ThemeReflector
                                                        .textColor(
                                                      context,
                                                      importance: TextImportance
                                                          .secondary,
                                                    ),
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                                const SizedBox(width: 7),
                                                Container(
                                                  width: 16,
                                                  height: 16,
                                                  decoration:
                                                      const BoxDecoration(
                                                    shape: BoxShape.circle,
                                                  ),
                                                  child: Icon(
                                                    Icons.info_outline,
                                                    color: ThemeReflector
                                                        .iconColor(context,
                                                            opacity: 0.6),
                                                    size: 16,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            // Add Indicator button
                                            ElevatedButton.icon(
                                              onPressed: () {
                                                _showAddIndicatorDialog();
                                              },
                                              icon: Icon(
                                                Icons.add,
                                                color: colorScheme.primary,
                                                size: 16,
                                              ),
                                              label: Text(
                                                'Add Indicator',
                                                style: TextStyle(
                                                  color: colorScheme.primary,
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: colorScheme
                                                    .primary
                                                    .withOpacity(0.1),
                                                foregroundColor:
                                                    colorScheme.primary,
                                                elevation: 0,
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 10,
                                                        vertical: 8),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 16),
                                        // Indicator cards
                                        indicators.isEmpty
                                            ? Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        vertical: 24.0),
                                                child: Text(
                                                  'No indicators added yet. Click "Add Indicator" to add one.',
                                                  style: TextStyle(
                                                    color: ThemeReflector
                                                        .textColor(
                                                      context,
                                                      importance: TextImportance
                                                          .tertiary,
                                                    ),
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                  textAlign: TextAlign.center,
                                                ),
                                              )
                                            : Column(
                                                children: indicators
                                                    .map((indicator) =>
                                                        _buildIndicatorCard(
                                                            indicator,
                                                            theme,
                                                            textTheme))
                                                    .toList(),
                                              ),

                                        if (indicators.isNotEmpty)
                                          const SizedBox(height: 16),

                                        // Minimum indicators required
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Minimum Indicator number triggers required for exit',
                                              style: TextStyle(
                                                color: ThemeReflector.textColor(
                                                  context,
                                                  importance:
                                                      TextImportance.secondary,
                                                ),
                                                fontSize: 14,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            ),
                                            const SizedBox(height: 12),
                                            Row(
                                              children: [
                                                Text(
                                                  '${minIndicators.toInt()} out of ${indicators.length}',
                                                  style: TextStyle(
                                                    color: ThemeReflector
                                                        .textColor(
                                                      context,
                                                      importance: TextImportance
                                                          .primary,
                                                    ),
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                                Expanded(
                                                  child: SliderTheme(
                                                    data: SliderThemeData(
                                                      trackHeight: 6,
                                                      activeTrackColor:
                                                          colorScheme.primary,
                                                      inactiveTrackColor:
                                                          colorScheme.primary
                                                              .withOpacity(0.2),
                                                      thumbColor: theme
                                                          .scaffoldBackgroundColor,
                                                      thumbShape:
                                                          const RoundSliderThumbShape(
                                                        enabledThumbRadius: 8,
                                                        elevation: 4,
                                                      ),
                                                      overlayColor: colorScheme
                                                          .primary
                                                          .withOpacity(0.2),
                                                    ),
                                                    child: Slider(
                                                      value: minIndicators,
                                                      min: 0,
                                                      max: indicators.isEmpty
                                                          ? 0
                                                          : indicators.length
                                                              .toDouble(),
                                                      divisions: indicators
                                                              .isNotEmpty
                                                          ? indicators.length
                                                          : 1,
                                                      onChanged: (value) {
                                                        setState(() {
                                                          minIndicators = value;
                                                        });
                                                      },
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ], // Close inner else clause
                              ], // Close outer else clause
                            ], // Close if (botType != 'sell')
                          ],
                        ),
                      ),
                    ),

                    // Bottom next button
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: ThemeReflector.surfaceColor(context),
                        border: Border(
                          top: BorderSide(
                            color: ThemeReflector.dividerColor(context),
                            width: 1,
                          ),
                        ),
                        boxShadow: ThemeReflector.cardShadow(context),
                      ),
                      child: SizedBox(
                        width: double.infinity,
                        height: 48,
                        child: ElevatedButton(
                          onPressed: () {
                            // Validate profit and stop loss values
                            if (takeProfit > 100) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Take Profit value ($takeProfit%) exceeds maximum allowed (100%). Please reduce the value.',
                                  ),
                                  backgroundColor: ThemeReflector.statusColor(
                                      context,
                                      status: StatusType.error),
                                  duration: const Duration(seconds: 5),
                                ),
                              );
                              return;
                            }

                            if (stopLoss > 100) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Stop Loss value ($stopLoss%) exceeds maximum allowed (100%). Please reduce the value.',
                                  ),
                                  backgroundColor: ThemeReflector.statusColor(
                                      context,
                                      status: StatusType.error),
                                  duration: const Duration(seconds: 5),
                                ),
                              );
                              return;
                            }

                            // Validate Price bot sell price
                            if (botType == 'price') {
                              if (_sellPriceController.text.isEmpty ||
                                  double.tryParse(_sellPriceController.text) ==
                                      null ||
                                  double.parse(_sellPriceController.text) <=
                                      0) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: const Text(
                                        'Please enter a valid positive price for sell condition.'),
                                    backgroundColor: ThemeReflector.statusColor(
                                      context,
                                      status: StatusType.error,
                                    ),
                                  ),
                                );
                                return;
                              }
                            }

                            // Save exit settings
                            _saveExitSettings();

                            // Get arguments from current route
                            final currentArgs = ModalRoute.of(context)
                                ?.settings
                                .arguments as Map<String, dynamic>?;

                            print('=== EXIT SCREEN STRATEGY DEBUG ===');
                            print(
                                'currentArgs strategy: ${currentArgs?['strategy']}');
                            print(
                                'Passing strategy: ${currentArgs?['strategy'] ?? 'Long'}');
                            print('==================================');

                            // Navigation ready

                            // Navigate to the deploy screen with all required arguments
                            Navigator.pushNamed(
                                context, AppConstants.deployBotRoute,
                                arguments: {
                                  'isEditing':
                                      currentArgs?['isEditing'] ?? false,
                                  'botId': currentArgs?['botId'],
                                  'botType': botType,
                                  // Only pass through strategy to avoid conflicts
                                  'strategy': currentArgs?['strategy'] ??
                                      'Long', // Safe fallback
                                });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Theme.of(context).colorScheme.primary,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'Next',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onPrimary,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                )),
    );
  }

  Widget _buildHeader(
      ThemeData theme, ColorScheme colorScheme, TextTheme textTheme) {
    // Choose image based on bot type
    String imagePath;
    switch (botType?.toLowerCase()) {
      case 'grid':
        imagePath = 'assets/images/grid_exit.png';
        break;
      case 'momentum':
        imagePath = 'assets/images/momentum_exit.png';
        break;
      default:
        imagePath = 'assets/images/trading_exit.png';
        break;
    }

    return Center(
      child: Column(
        children: [
          const SizedBox(height: 16),
          Image.asset(
            imagePath,
            width: 120,
            height: 120,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.logout,
                      size: 48,
                      color: colorScheme.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      "Exit",
                      style: TextStyle(
                        color: ThemeReflector.textColor(
                          context,
                          importance: TextImportance.primary,
                        ),
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                "Exit",
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.primary,
                  ),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          // Step indicator
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: colorScheme.primary.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildSection({
    required ThemeData theme,
    required String title,
    required Widget child,
    bool isSwitch = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeReflector.screenBackground(context),
        border: Border(
          bottom: BorderSide(
            color: ThemeReflector.dividerColor(context),
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: isSwitch
                ? MainAxisAlignment.spaceBetween
                : MainAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: ThemeReflector.textColor(
                        context,
                        importance: TextImportance.secondary,
                      ),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 7),
                  Container(
                    width: 16,
                    height: 16,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.info_outline,
                      color: ThemeReflector.iconColor(context, opacity: 0.6),
                      size: 16,
                    ),
                  ),
                ],
              ),
              if (isSwitch) child,
            ],
          ),
          if (!isSwitch) const SizedBox(height: 10),
          if (!isSwitch) child,
        ],
      ),
    );
  }

  // Method giống hệt Trade Parameter để đảm bảo màu sắc giống nhau
  Widget _buildTradeParameterSlider({
    required double value,
    required ValueChanged<double> onChanged,
    required String displayValue,
    double min = 0.1,
    double max = 100,
    int? divisions, // Thêm divisions parameter để làm discrete slider
  }) {
    final theme = Theme.of(context);

    // Đảm bảo giá trị nằm trong khoảng min và max
    double safeValue = value;
    if (safeValue < min) safeValue = min;
    if (safeValue > max) safeValue = max;

    // Prevent infinite precision issues
    safeValue = double.parse(safeValue.toStringAsFixed(2));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: SliderTheme(
                data: SliderThemeData(
                  trackHeight: 8,
                  thumbColor: theme.colorScheme.primary,
                  activeTrackColor: theme.colorScheme.primary,
                  inactiveTrackColor:
                      theme.colorScheme.primary.withOpacity(0.2),
                  overlayColor: theme.colorScheme.primary.withOpacity(0.2),
                ),
                child: Slider(
                  value: safeValue,
                  min: min,
                  max: max,
                  divisions: divisions, // Thêm divisions để làm discrete slider
                  onChanged: onChanged,
                ),
              ),
            ),
            Container(
              width: 90,
              height: 48,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              decoration: BoxDecoration(
                border: Border.all(
                  color: ThemeReflector.borderColor(context),
                ),
                borderRadius: BorderRadius.circular(8),
                color: ThemeReflector.surfaceColor(context),
              ),
              child: Center(
                child: Text(
                  displayValue,
                  style: TextStyle(
                    color: ThemeReflector.textColor(context,
                        importance: TextImportance.primary),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSliderWithValue({
    required ThemeData theme,
    required double value,
    required ValueChanged<double> onChanged,
    required String displayValue,
    Color color = const Color(0xFF14B377),
    double min = 0.1,
    double max = 100,
  }) {
    double safeValue = value;
    if (safeValue < min) safeValue = min;
    if (safeValue > max) safeValue = max;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          displayValue,
          style: TextStyle(
            color: color,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        Row(
          children: [
            Expanded(
              child: SliderTheme(
                data: SliderThemeData(
                  trackHeight: 6,
                  activeTrackColor: color,
                  inactiveTrackColor:
                      ThemeReflector.surfaceColor(context).withOpacity(0.5),
                  thumbColor: Theme.of(context).colorScheme.onPrimary,
                  thumbShape: const RoundSliderThumbShape(
                    enabledThumbRadius: 8,
                    elevation: 4,
                  ),
                  overlayColor: color.withOpacity(0.2),
                ),
                child: Slider(
                  value: safeValue,
                  min: min,
                  max: max,
                  onChanged: onChanged,
                ),
              ),
            ),
            Container(
              width: 90,
              height: 48,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              decoration: BoxDecoration(
                border: Border.all(
                  color: ThemeReflector.borderColor(context),
                ),
                borderRadius: BorderRadius.circular(8),
                color: ThemeReflector.surfaceColor(context),
              ),
              child: Center(
                child: Text(
                  displayValue,
                  style: TextStyle(
                    color: ThemeReflector.textColor(context,
                        importance: TextImportance.primary),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildIndicatorCard(
      Map<String, dynamic> indicator, ThemeData theme, TextTheme textTheme) {
    final String indicatorName = indicator['name'] ?? 'Unknown';
    final String? description = indicator['description'];

    // Create a formatted string with the indicator parameters
    String parameterText = '';
    if (indicator['period_num'] != null) {
      parameterText += 'Period: ${indicator['period_num']}';
    }

    if (indicator['value2'] != null) {
      parameterText += parameterText.isNotEmpty ? ', ' : '';
      parameterText += 'Value 2: ${indicator['value2']}';
    }

    if (indicator['value3'] != null) {
      parameterText += parameterText.isNotEmpty ? ', ' : '';
      parameterText += 'Value 3: ${indicator['value3']}';
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        border: Border.all(
          color: ThemeReflector.borderColor(context),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
        color: ThemeReflector.surfaceColor(context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  indicatorName,
                  style: textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Row(
                children: [
                  IconButton(
                    icon: Icon(
                      Icons.settings,
                      color: textTheme.bodyMedium?.color,
                      size: 20,
                    ),
                    onPressed: () {
                      // Show indicator settings
                      _showIndicatorSettings(indicator);
                    },
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const SizedBox(width: 16),
                  IconButton(
                    icon: Icon(
                      Icons.close,
                      color: textTheme.bodySmall?.color,
                      size: 20,
                    ),
                    onPressed: () {
                      setState(() {
                        indicators.remove(indicator);
                        // Adjust min indicators if necessary
                        if (minIndicators > indicators.length) {
                          minIndicators = indicators.length.toDouble();
                        }
                      });
                    },
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ],
          ),
          if (parameterText.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: Text(
                parameterText,
                style: textTheme.bodySmall,
              ),
            ),
          if (description != null && description.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: Text(
                description,
                style: textTheme.bodySmall?.copyWith(
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Filter indicators based on bot type
  /// Simple bot can show Volume indicator (backend might need it)
  /// Other bot types filter out Volume as per web behavior
  List<Map<String, dynamic>> _getFilteredIndicators() {
    print('Exit Screen - Bot Type: $botType');
    print('Exit Screen - Available indicators: ${availableIndicators.length}');

    if (botType?.toLowerCase() == 'simple') {
      // Simple bot: Show ALL indicators including Volume (backend might need it)
      print(
          'Exit Screen - Simple bot: Showing all indicators including Volume');
      return availableIndicators;
    } else {
      // Other bot types: Filter out Volume indicator as per web behavior
      final filtered = availableIndicators.where((indicator) {
        final name = indicator['name']?.toString().toLowerCase() ?? '';
        return name != 'volume';
      }).toList();

      print('Exit Screen - $botType bot: Filtered out Volume indicator');
      print('Exit Screen - Filtered indicators: ${filtered.length}');
      print(
          'Exit Screen - Available indicators: ${filtered.map((i) => i['name']).toList()}');
      return filtered;
    }
  }

  void _showAddIndicatorDialog() {
    if (availableIndicators.isEmpty && !isLoadingIndicators) {
      _fetchIndicators();
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Add Indicator',
          style: TextStyle(
            color: ThemeReflector.textColor(
              context,
              importance: TextImportance.primary,
            ),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: isLoadingIndicators
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20.0),
                    child: CircularProgressIndicator(),
                  ),
                )
              : availableIndicators.isEmpty
                  ? const Center(
                      child: Text('No indicators available'),
                    )
                  : ListView.builder(
                      shrinkWrap: true,
                      itemCount: _getFilteredIndicators().length,
                      itemBuilder: (context, index) {
                        final indicator = _getFilteredIndicators()[index];
                        final String name = indicator['name'] ?? 'Unknown';

                        // Create a formatted string with important parameters
                        String paramInfo =
                            'Period: ${indicator['period_num'] ?? ''}';
                        if (indicator['value2'] != null) {
                          paramInfo += ', Value2: ${indicator['value2']}';
                        }

                        return ListTile(
                          title: Text(name),
                          subtitle: Text(paramInfo),
                          onTap: () {
                            setState(() {
                              // Clone the indicator to avoid modifying the original
                              final indicatorToAdd =
                                  Map<String, dynamic>.from(indicator);

                              // Đảm bảo có đầy đủ fields theo format ví dụ
                              indicatorToAdd.addAll({
                                'id': null,
                                'description': null,
                                'value4': null,
                                'value5': null,
                                'value6': null,
                                'value7': null,
                                'value8': null,
                                'value9': null,
                                'value10': null,
                                'value11': null,
                                'value12': null,
                                'value13': null,
                                'value14': null,
                                'value15': null,
                                'value16': null,
                                'value17': null,
                                'value18': null,
                                'value19': null,
                                'value20': null,
                                'created_at': null,
                                'updated_at': null,
                                'ind_key':
                                    DateTime.now().millisecondsSinceEpoch,
                                'indicator_id': indicatorToAdd['id'] ?? 1,
                                'type': 'exit'
                              });

                              indicators.add(indicatorToAdd);
                            });
                            Navigator.pop(context);
                          },
                        );
                      },
                    ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(color: Theme.of(context).colorScheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  void _showIndicatorSettings(Map<String, dynamic> indicator) {
    // Create controllers for each editable field
    final periodController =
        TextEditingController(text: indicator['period_num']?.toString() ?? '');
    final value2Controller =
        TextEditingController(text: indicator['value2']?.toString() ?? '');
    final value3Controller =
        TextEditingController(text: indicator['value3']?.toString() ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white, // ✅ Set dialog background to white
        title: Text(
          'Edit ${indicator['name']}',
          style: const TextStyle(
            color: Colors.black, // ✅ Set title color to black
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Period',
                style: TextStyle(
                  color: Colors.black, // ✅ Set label color to black
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: periodController,
                keyboardType: TextInputType.number,
                style: const TextStyle(color: Colors.black),
                decoration: InputDecoration(
                  filled: true,
                  fillColor: Colors.white, // ✅ Set input background to white
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFE8EAED)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFE8EAED)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFF007AFF)),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Value 2',
                style: TextStyle(
                  color: Colors.black, // ✅ Set label color to black
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: value2Controller,
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                style: const TextStyle(color: Colors.black),
                decoration: InputDecoration(
                  filled: true,
                  fillColor: Colors.white, // ✅ Set input background to white
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFE8EAED)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFE8EAED)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFF007AFF)),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Value 3',
                style: TextStyle(
                  color: Colors.black, // ✅ Set label color to black
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: value3Controller,
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                style: const TextStyle(color: Colors.black),
                decoration: InputDecoration(
                  filled: true,
                  fillColor: Colors.white, // ✅ Set input background to white
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFE8EAED)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFE8EAED)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFF007AFF)),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(
                  color: ThemeReflector.textColor(context,
                      importance: TextImportance.secondary)),
            ),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                // Update indicator values
                indicator['period_num'] = int.tryParse(periodController.text) ??
                    indicator['period_num'];
                indicator['value2'] = double.tryParse(value2Controller.text) ??
                    indicator['value2'];
                indicator['value3'] = double.tryParse(value3Controller.text) ??
                    indicator['value3'];
              });
              Navigator.pop(context);
            },
            child: Text(
              'Save',
              style: TextStyle(color: Theme.of(context).colorScheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  // Method to save exit settings
  void _saveExitSettings() {
    // Get the provider
    final botConfigProvider =
        Provider.of<BotConfigProvider>(context, listen: false);
    final currentConfig = botConfigProvider.botConfig;

    // Format the indicators list for display in UI
    List<String> formattedIndicators = [];
    for (var indicator in indicators) {
      String indicatorText = indicator['name'] ?? 'Unknown';

      // Add parameters if available
      List<String> params = [];
      if (indicator['period_num'] != null) {
        params.add('${indicator['period_num']}');
      }
      if (indicator['value2'] != null) {
        params.add('${indicator['value2']}');
      }
      if (indicator['value3'] != null) {
        params.add('${indicator['value3']}');
      }

      // Add parameters in parentheses if there are any
      if (params.isNotEmpty) {
        indicatorText += ' (${params.join(', ')})';
      }

      formattedIndicators.add(indicatorText);
    }

    // Format trading view alerts (keep existing or empty if not available)
    List<String> tradingViewAlerts = currentConfig.exits.tradingView;

    // Format min indicators text
    String minIndicatorsText =
        "${minIndicators.toInt()} out of ${indicators.length} Indicators";

    // Create updated exits
    final updatedExits = BotExits(
      takeProfit: '${takeProfit.toStringAsFixed(1)}%',
      stopLoss: '${stopLoss.toStringAsFixed(1)}%', // Add stopLoss to BotExits
      tradingView: tradingViewAlerts,
      exitIndicators: formattedIndicators,
      minIndicatorsRequired: minIndicatorsText,
      stopAfterCurrentDeal: currentConfig.exits.stopAfterCurrentDeal,
      autoCloseAtEndOfDay: currentConfig.exits.autoCloseAtEndOfDay,
      // Price bot specific field
      priceExit: botType == 'price'
          ? _sellPriceController.text
          : null, // ✅ Save price exit
      // Sell bot specific fields
      sellPriceHigher:
          botType == 'sell' ? _sellPriceForSellBotController.text : null,
      sellPriceLower: botType == 'sell' ? _sellPriceLowerController.text : null,
      minimumProfitForTrigger:
          botType == 'sell' ? minimumProfitForTrigger : null,
      minIndicators: botType == 'sell' ? minIndicators : null,
      // Grid bot specific fields
      gridWaitTime: botType?.toLowerCase() == 'grid' ? gridWaitTime : null,
      gridOuterRangeLimit:
          botType?.toLowerCase() == 'grid' ? gridOuterRangeLimit : null,
      gridExitStrategyNoTrade:
          botType?.toLowerCase() == 'grid' ? selectedScenario1Option : null,
      gridExitStrategyHasTrade:
          botType?.toLowerCase() == 'grid' ? selectedScenario2Option : null,
      // Advanced bot specific fields
      advancedTakeProfitType:
          botType?.toLowerCase() == 'advanced' ? selectedTakeProfitType : null,
      fixedTrailingSellPercent: botType?.toLowerCase() == 'advanced' &&
              selectedTakeProfitType == 'Fixed Trailing Take Profit'
          ? _fixedTrailingSellPercentController.text
          : null,
      fixedTrailingPricePercent: botType?.toLowerCase() == 'advanced' &&
              selectedTakeProfitType == 'Fixed Trailing Take Profit'
          ? _fixedTrailingPricePercentController.text
          : null,
      variableTrailingEntries: botType?.toLowerCase() == 'advanced' &&
              selectedTakeProfitType == 'Variable Trailing Take Profit'
          ? variableTrailingControllers
              .map((controllers) => {
                    'sellPercent': controllers['sellPercent']!.text,
                    'pricePercent': controllers['pricePercent']!.text,
                  })
              .toList()
          : null,
    );

    // Update the provider
    botConfigProvider.updateExits(updatedExits);

    // Price bot specific debug
    if (botType == 'price') {
      print('Price Exit (Price Bot): ${_sellPriceController.text}');
    }
  }

  // Phương thức kiểm tra giá trị slider
  void _ensureValidSliderValues() {
    print("CHECKING EXIT SCREEN SLIDER VALUES:");
    print("takeProfit: $takeProfit (should be >= 0.1 and <= 100)");
    print("stopLoss: $stopLoss (should be >= 0.1 and <= 100)");

    bool hasChanged = false;

    if (takeProfit < 0.1) {
      takeProfit = 0.1;
      hasChanged = true;
      print("WARNING: Fixed takeProfit to minimum value: 0.1");
    }

    if (stopLoss < 0.1) {
      stopLoss = 0.1;
      hasChanged = true;
      print("WARNING: Fixed stopLoss to minimum value: 0.1");
    }

    if (hasChanged) {
      setState(() {});
      print("Fixed slider values to ensure they are valid.");
    } else {
      print("All slider values are valid.");
    }
  }

  // Build sell price input section for price bot type (giống Fund Allocation)
  Widget _buildSellPriceInputSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeReflector.screenBackground(context),
        border: Border(
          bottom: BorderSide(
            color: ThemeReflector.dividerColor(context),
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sell when price is more than or equal to',
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.secondary,
              ),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _sellPriceController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: ThemeReflector.borderColor(context),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: ThemeReflector.borderColor(context),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: theme.colorScheme.primary,
                ),
              ),
              hintText: 'Enter price',
              helperText: 'Enter sell price in USD',
            ),
            onChanged: (_) => setState(() {}),
          ),
        ],
      ),
    );
  }

  // Build sell bot input sections for sell bot type
  Widget _buildSellBotInputSections(ThemeData theme) {
    return Column(
      children: [
        // Sell when price is more than or equal to
        _buildSection(
          theme: theme,
          title: 'Sell when price is more than or equal to',
          child: TextField(
            controller: _sellPriceForSellBotController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: ThemeReflector.borderColor(context),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: ThemeReflector.borderColor(context),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: theme.colorScheme.primary,
                ),
              ),
              hintText: 'Enter sell price',
              helperText: 'Enter price in USD',
            ),
          ),
        ),

        // Sell when price is lower than or equal to
        _buildSection(
          theme: theme,
          title: 'Sell when price is lower than or equal to',
          child: TextField(
            controller: _sellPriceLowerController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: ThemeReflector.borderColor(context),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: ThemeReflector.borderColor(context),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: theme.colorScheme.primary,
                ),
              ),
              hintText: 'Enter sell price',
              helperText: 'Enter price in USD',
            ),
          ),
        ),

        // Trading View Toggle (giống Price bot)
        _buildSection(
          theme: theme,
          title: 'TradingView Integration',
          child: Switch(
            value: sellBotTradingViewEnabled,
            onChanged: (value) {
              setState(() {
                sellBotTradingViewEnabled = value;
              });
            },
            activeColor: theme.colorScheme.primary,
            activeTrackColor: theme.colorScheme.primary.withOpacity(0.5),
          ),
          isSwitch: true,
        ),

        // Indicator Selection (giống Price bot)
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'INDICATORS',
                    style: TextStyle(
                      color: ThemeReflector.textColor(
                        context,
                        importance: TextImportance.secondary,
                      ),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: () {
                      _showAddIndicatorDialog();
                    },
                    icon: Icon(
                      Icons.add,
                      color: theme.colorScheme.primary,
                      size: 16,
                    ),
                    label: Text(
                      'Add Indicator',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          theme.colorScheme.primary.withOpacity(0.1),
                      foregroundColor: theme.colorScheme.primary,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Show indicator cards if any indicators are added
              if (indicators.isNotEmpty) ...[
                ...indicators
                    .map((indicator) => Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: _buildIndicatorCard(
                              indicator, theme, theme.textTheme),
                        ))
                    .toList(),
                const SizedBox(height: 16),
              ],

              // Minimum indicators required (copy y hệt từ Simple bot entry)
              if (indicators.isNotEmpty) ...[
                const SizedBox(height: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Minimum Indicator number triggers required for exit',
                      style: TextStyle(
                        color: ThemeReflector.textColor(
                          context,
                          importance: TextImportance.secondary,
                        ),
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Text(
                          '${minIndicators.toInt()} out of ${indicators.length}',
                          style: TextStyle(
                            color: ThemeReflector.textColor(
                              context,
                              importance: TextImportance.primary,
                            ),
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Expanded(
                          child: SliderTheme(
                            data: SliderThemeData(
                              trackHeight: 6,
                              activeTrackColor: ThemeReflector.statusColor(
                                  context,
                                  status: StatusType.success),
                              inactiveTrackColor:
                                  ThemeReflector.surfaceColor(context),
                              thumbColor:
                                  Theme.of(context).colorScheme.onPrimary,
                              thumbShape: const RoundSliderThumbShape(
                                enabledThumbRadius: 8,
                                elevation: 4,
                              ),
                              overlayColor: ThemeReflector.statusColor(context,
                                      status: StatusType.success)
                                  .withOpacity(0.2),
                            ),
                            child: Slider(
                              value: minIndicators,
                              min: 0,
                              max: indicators.isEmpty
                                  ? 0
                                  : indicators.length.toDouble(),
                              divisions:
                                  indicators.isNotEmpty ? indicators.length : 1,
                              onChanged: (value) {
                                setState(() {
                                  minIndicators = value;
                                });
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),

        // Minimum Profit for Indicator Trigger (slider giống Trade Parameter)
        _buildSection(
          theme: theme,
          title: 'Minimum Profit for Indicator Trigger',
          child: _buildTradeParameterSlider(
            value: minimumProfitForTrigger,
            onChanged: (value) {
              setState(() {
                minimumProfitForTrigger = value;
              });
            },
            displayValue: '\$${minimumProfitForTrigger.toStringAsFixed(2)}',
            min: 0.1,
            max: 100.0,
          ),
        ),
      ],
    );
  }

  // Build Quickstart-specific Exit UI (simplified)
  Widget _buildQuickstartExitSection(ThemeData theme) {
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Take Profit Slider (sử dụng _buildSection và _buildSliderWithValue hiện có)
        _buildSection(
          theme: theme,
          title: 'Take Profit',
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSliderWithValue(
                theme: theme,
                value: takeProfit,
                onChanged: (value) {
                  setState(() {
                    takeProfit = value;
                  });
                },
                displayValue: '${takeProfit.toStringAsFixed(1)}%',
                color: const Color(0xFF14B377),
                min: 0.1,
                max: 100,
              ),
              const SizedBox(height: 8),
              Text(
                'Note: Values above 100% may cause deployment errors',
                style: TextStyle(
                  fontSize: 12,
                  color: takeProfit > 100
                      ? ThemeReflector.statusColor(context,
                          status: StatusType.error)
                      : ThemeReflector.textColor(context,
                          importance: TextImportance.secondary),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),

        // Stop Loss Slider (sử dụng _buildSection và _buildSliderWithValue hiện có)
        _buildSection(
          theme: theme,
          title: 'Stop Loss',
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSliderWithValue(
                theme: theme,
                value: stopLoss,
                onChanged: (value) {
                  setState(() {
                    stopLoss = value;
                  });
                },
                displayValue: '${stopLoss.toStringAsFixed(1)}%',
                color: const Color(0xFFF16A6C),
                min: 0.1,
                max: 100,
              ),
              const SizedBox(height: 8),
              Text(
                'Note: Values above 100% may cause deployment errors',
                style: TextStyle(
                  fontSize: 12,
                  color: stopLoss > 100
                      ? ThemeReflector.statusColor(context,
                          status: StatusType.error)
                      : ThemeReflector.textColor(context,
                          importance: TextImportance.secondary),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),

        // TradingView Integration (sử dụng _buildSection hiện có)
        _buildSection(
          theme: theme,
          title: 'TradingView Integration',
          child: Switch(
            value: tradingViewEnabled,
            onChanged: (value) {
              setState(() {
                tradingViewEnabled = value;
              });
            },
            activeColor: theme.colorScheme.primary,
            activeTrackColor: theme.colorScheme.primary.withOpacity(0.5),
          ),
          isSwitch: true,
        ),

        // Exit Indicator Selection
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: ThemeReflector.surfaceColor(context),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: ThemeReflector.borderColor(context),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        'Exit Indicator Triggers',
                        style: TextStyle(
                          color: ThemeReflector.textColor(
                            context,
                            importance: TextImportance.secondary,
                          ),
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 7),
                      Container(
                        width: 16,
                        height: 16,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.info_outline,
                          color:
                              ThemeReflector.iconColor(context, opacity: 0.6),
                          size: 16,
                        ),
                      ),
                    ],
                  ),
                  // Add Indicator button (giống hệt như UI bình thường)
                  ElevatedButton.icon(
                    onPressed: () {
                      _showAddIndicatorDialog();
                    },
                    icon: Icon(
                      Icons.add,
                      color: theme.colorScheme.primary,
                      size: 16,
                    ),
                    label: Text(
                      'Add Indicator',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          theme.colorScheme.primary.withOpacity(0.1),
                      foregroundColor: theme.colorScheme.primary,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Indicator cards (sử dụng _buildIndicatorCard hiện có)
              indicators.isEmpty
                  ? Padding(
                      padding: const EdgeInsets.symmetric(vertical: 24.0),
                      child: Text(
                        'No indicators added yet. Click "Add Indicator" to add one.',
                        style: TextStyle(
                          color: ThemeReflector.textColor(
                            context,
                            importance: TextImportance.tertiary,
                          ),
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    )
                  : Column(
                      children: indicators
                          .map((indicator) => _buildIndicatorCard(
                              indicator, theme, theme.textTheme))
                          .toList(),
                    ),

              if (indicators.isNotEmpty) const SizedBox(height: 16),

              // Minimum indicators required (giống hệt UI bình thường)
              if (indicators.isNotEmpty) ...[
                const SizedBox(height: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Minimum Indicator number triggers required for exit',
                      style: TextStyle(
                        color: ThemeReflector.textColor(
                          context,
                          importance: TextImportance.secondary,
                        ),
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Text(
                          '${minIndicators.toInt()} out of ${indicators.length}',
                          style: TextStyle(
                            color: ThemeReflector.textColor(
                              context,
                              importance: TextImportance.primary,
                            ),
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Expanded(
                          child: SliderTheme(
                            data: SliderThemeData(
                              trackHeight: 6,
                              activeTrackColor: theme.colorScheme.primary,
                              inactiveTrackColor:
                                  theme.colorScheme.primary.withOpacity(0.2),
                              thumbColor: theme.scaffoldBackgroundColor,
                              thumbShape: const RoundSliderThumbShape(
                                enabledThumbRadius: 8,
                                elevation: 4,
                              ),
                              overlayColor:
                                  theme.colorScheme.primary.withOpacity(0.2),
                            ),
                            child: Slider(
                              value: minIndicators,
                              min: 0,
                              max: indicators.isEmpty
                                  ? 0
                                  : indicators.length.toDouble(),
                              divisions:
                                  indicators.isNotEmpty ? indicators.length : 1,
                              onChanged: (value) {
                                setState(() {
                                  minIndicators = value;
                                });
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  // Build Grid-specific Exit UI
  Widget _buildGridExitSection(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Wait Time Section
          _buildSection(
            theme: theme,
            title: '',
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      'Wait Time',
                      style: TextStyle(
                        color: ThemeReflector.textColor(context,
                            importance: TextImportance.secondary),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      Icons.info_outline,
                      size: 16,
                      color: ThemeReflector.iconColor(context),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  '${gridWaitTime.toInt()} Minutes',
                  style: TextStyle(
                    color: ThemeReflector.textColor(context,
                        importance: TextImportance.primary),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 12),
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    trackHeight: 6,
                    activeTrackColor: theme.colorScheme.primary,
                    inactiveTrackColor:
                        ThemeReflector.surfaceColor(context).withOpacity(0.5),
                    thumbColor: Theme.of(context).colorScheme.onPrimary,
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 8,
                      elevation: 4,
                    ),
                    overlayColor: theme.colorScheme.primary.withOpacity(0.2),
                  ),
                  child: Slider(
                    value: gridWaitTime,
                    min: 0,
                    max: 60,
                    divisions: 60,
                    onChanged: (value) {
                      setState(() {
                        gridWaitTime = value;
                      });
                    },
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '0 Minutes',
                      style: TextStyle(
                        color: ThemeReflector.textColor(context,
                            importance: TextImportance.tertiary),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Outer Range Limit Section
          _buildSection(
            theme: theme,
            title: '',
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      'Outer Range Limit',
                      style: TextStyle(
                        color: ThemeReflector.textColor(context,
                            importance: TextImportance.secondary),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      Icons.info_outline,
                      size: 16,
                      color: ThemeReflector.iconColor(context),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: TextEditingController(text: gridOuterRangeLimit),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 14),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                          color: ThemeReflector.borderColor(context)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                          color: ThemeReflector.borderColor(context)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: theme.colorScheme.primary),
                    ),
                    hintText: '0%',
                  ),
                  onChanged: (value) {
                    setState(() {
                      gridOuterRangeLimit = value;
                    });
                  },
                ),
              ],
            ),
          ),

          // First Scenario Section
          SizedBox(
            width: double.infinity,
            child: _buildGridScenarioSection(
              theme: theme,
              title: 'If Wait Time is reached and no grid trades are filled',
              options: [
                'Cancel all open grid trades and restart Grid Bot',
                'Cancel all open grid trades and stop Bot',
                'Leave all grid trades open and stop Bot',
              ],
              selectedIndex: selectedScenario1Option,
              onSelectionChanged: (index) {
                setState(() {
                  selectedScenario1Option = index;
                });
              },
            ),
          ),

          // Second Scenario Section
          SizedBox(
            width: double.infinity,
            child: _buildGridScenarioSection(
              theme: theme,
              title:
                  'If Wait Time or Outer Range Limit is reached and at least one grid trade is filled',
              options: [
                'Cancel all open grid trades and restart Grid Bot',
                'Do nothing and restart Grid Bot',
                'Leave remaining Grid trades open and stop Bot',
                'Do nothing and keep Grid Bot running',
              ],
              selectedIndex: selectedScenario2Option,
              onSelectionChanged: (index) {
                setState(() {
                  selectedScenario2Option = index;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGridScenarioSection({
    required ThemeData theme,
    required String title,
    required List<String> options,
    required int selectedIndex,
    required Function(int) onSelectionChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeReflector.surfaceColor(context),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              color: ThemeReflector.textColor(context,
                  importance: TextImportance.primary),
              fontSize: 16,
              fontWeight: FontWeight.w600,
              height: 1.3,
            ),
            maxLines: null,
            overflow: TextOverflow.visible,
          ),
          const SizedBox(height: 16),
          Column(
            children: options.asMap().entries.map((entry) {
              int index = entry.key;
              String option = entry.value;
              bool isSelected = index == selectedIndex;

              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      onSelectionChanged(index);
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? theme.colorScheme.primary.withOpacity(0.1)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected
                              ? theme.colorScheme.primary
                              : ThemeReflector.borderColor(context),
                          width: 1,
                        ),
                      ),
                      child: IntrinsicHeight(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: 20,
                              height: 20,
                              margin: const EdgeInsets.only(top: 2),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: isSelected
                                      ? theme.colorScheme.primary
                                      : ThemeReflector.borderColor(context),
                                  width: 2,
                                ),
                              ),
                              child: isSelected
                                  ? Center(
                                      child: Container(
                                        width: 10,
                                        height: 10,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: theme.colorScheme.primary,
                                        ),
                                      ),
                                    )
                                  : null,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                option,
                                style: TextStyle(
                                  color: ThemeReflector.textColor(context,
                                      importance: TextImportance.primary),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  height: 1.4,
                                ),
                                maxLines: null,
                                overflow: TextOverflow.visible,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  // Build Momentum-specific Exit UI
  Widget _buildMomentumExitSection(ThemeData theme) {
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Take Profit Section
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'TAKE PROFIT',
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.secondary,
                  ),
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 1.2,
                ),
              ),
              const SizedBox(height: 16),
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  trackHeight: 6,
                  activeTrackColor: colorScheme.primary,
                  inactiveTrackColor: Colors.grey[300],
                  thumbColor: colorScheme.primary,
                  overlayColor: colorScheme.primary.withValues(alpha: 0.2),
                  thumbShape: const RoundSliderThumbShape(
                    enabledThumbRadius: 8,
                  ),
                  trackShape: const RoundedRectSliderTrackShape(),
                  overlayShape: const RoundSliderOverlayShape(
                    overlayRadius: 16,
                  ),
                ),
                child: Slider(
                  value: takeProfit,
                  min: 0,
                  max: 100,
                  divisions: 100,
                  onChanged: (value) {
                    setState(() {
                      takeProfit = value;
                    });
                  },
                ),
              ),
              Text(
                '${takeProfit.toStringAsFixed(1)}%',
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.primary,
                  ),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),

        // Stop Loss Section
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'STOP LOSS',
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.secondary,
                  ),
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 1.2,
                ),
              ),
              const SizedBox(height: 16),
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  trackHeight: 6,
                  activeTrackColor: colorScheme.primary,
                  inactiveTrackColor: Colors.grey[300],
                  thumbColor: colorScheme.primary,
                  overlayColor: colorScheme.primary.withValues(alpha: 0.2),
                  thumbShape: const RoundSliderThumbShape(
                    enabledThumbRadius: 8,
                  ),
                  trackShape: const RoundedRectSliderTrackShape(),
                  overlayShape: const RoundSliderOverlayShape(
                    overlayRadius: 16,
                  ),
                ),
                child: Slider(
                  value: stopLoss,
                  min: 0,
                  max: 100,
                  divisions: 100,
                  onChanged: (value) {
                    setState(() {
                      stopLoss = value;
                    });
                  },
                ),
              ),
              Text(
                '${stopLoss.toStringAsFixed(1)}%',
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.primary,
                  ),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),

        // Trading View Section
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  const Text(
                    'Trading view',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    Icons.help_outline,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                ],
              ),
              Switch(
                value: tradingViewEnabled,
                onChanged: (value) {
                  setState(() {
                    tradingViewEnabled = value;
                  });
                },
                activeColor: colorScheme.primary,
              ),
            ],
          ),
        ),

        // Add Indicator Section (giống các bot khác)
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'INDICATORS',
                    style: TextStyle(
                      color: ThemeReflector.textColor(
                        context,
                        importance: TextImportance.secondary,
                      ),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: () {
                      _showAddIndicatorDialog();
                    },
                    icon: Icon(
                      Icons.add,
                      color: colorScheme.primary,
                      size: 16,
                    ),
                    label: Text(
                      'Add Indicator',
                      style: TextStyle(
                        color: colorScheme.primary,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          colorScheme.primary.withValues(alpha: 0.1),
                      foregroundColor: colorScheme.primary,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Show indicator cards if any indicators are added
              if (indicators.isNotEmpty) ...[
                ...indicators
                    .map((indicator) => Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: _buildIndicatorCard(
                              indicator, theme, theme.textTheme),
                        ))
                    .toList(),
                const SizedBox(height: 16),

                // Indicator Triggers slider (chỉ hiện khi có indicators)
                Row(
                  children: [
                    const Text(
                      'Indicator Triggers',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      Icons.help_outline,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      '${indicatorTriggers.toInt()}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Text(
                      ' of ',
                      style: TextStyle(fontSize: 16),
                    ),
                    Text(
                      '${indicators.length}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Text(
                      ' Indicators',
                      style: TextStyle(fontSize: 16),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    trackHeight: 6,
                    activeTrackColor: colorScheme.primary,
                    inactiveTrackColor: Colors.grey[300],
                    thumbColor: colorScheme.primary,
                    overlayColor: colorScheme.primary.withValues(alpha: 0.2),
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 8,
                    ),
                    trackShape: const RoundedRectSliderTrackShape(),
                    overlayShape: const RoundSliderOverlayShape(
                      overlayRadius: 16,
                    ),
                  ),
                  child: Slider(
                    value: indicatorTriggers,
                    min: 0,
                    max: indicators.length.toDouble(),
                    divisions: indicators.isNotEmpty ? indicators.length : 1,
                    onChanged: (value) {
                      setState(() {
                        indicatorTriggers = value;
                      });
                    },
                  ),
                ),
              ] else
                Text(
                  'No indicators added yet. Click "Add Indicator" to add one.',
                  style: TextStyle(
                    color: ThemeReflector.textColor(
                      context,
                      importance: TextImportance.secondary,
                    ),
                    fontSize: 14,
                  ),
                ),
            ],
          ),
        ),

        // Minimum Profit for Indicator Trigger Section
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Text(
                    'Minimum Profit for Indicator Trigger',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    Icons.help_outline,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  trackHeight: 6,
                  activeTrackColor: colorScheme.primary,
                  inactiveTrackColor: Colors.grey[300],
                  thumbColor: colorScheme.primary,
                  overlayColor: colorScheme.primary.withValues(alpha: 0.2),
                  thumbShape: const RoundSliderThumbShape(
                    enabledThumbRadius: 8,
                  ),
                  trackShape: const RoundedRectSliderTrackShape(),
                  overlayShape: const RoundSliderOverlayShape(
                    overlayRadius: 16,
                  ),
                ),
                child: Slider(
                  value: minimumProfitForTrigger,
                  min: 0,
                  max: 100,
                  divisions: 100,
                  onChanged: (value) {
                    setState(() {
                      minimumProfitForTrigger = value;
                    });
                  },
                ),
              ),
              Text(
                '${minimumProfitForTrigger.toStringAsFixed(1)}%',
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.primary,
                  ),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),

        // Disable Bot Section (giống ảnh)
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Left side - Text
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Disable Bot',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'If stop loss is hit',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),

              // Right side - Input field và "in a row"
              Row(
                children: [
                  // Input field
                  SizedBox(
                    width: 60,
                    height: 40,
                    child: TextField(
                      textAlign: TextAlign.center,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        hintText: '0',
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 8),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                              color: ThemeReflector.borderColor(context),
                              width: 1.0),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                              color: ThemeReflector.borderColor(context),
                              width: 1.0),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                              color: colorScheme.primary, width: 1.0),
                        ),
                      ),
                      onChanged: (value) {
                        setState(() {
                          stopLossRowCount = int.tryParse(value) ?? 0;
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'in a row',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Bottom Checkboxes Section
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Column(
            children: [
              // Auto-close open positions at end of trading day
              Row(
                children: [
                  Checkbox(
                    value: autoClosePositions,
                    onChanged: (value) {
                      setState(() {
                        autoClosePositions = value ?? false;
                      });
                    },
                    activeColor: colorScheme.primary,
                  ),
                  const Expanded(
                    child: Text(
                      'Auto-close open positions at end of trading day',
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Stop after Current Deal Ends
              Row(
                children: [
                  Checkbox(
                    value: stopAfterCurrentDeal,
                    onChanged: (value) {
                      setState(() {
                        stopAfterCurrentDeal = value ?? false;
                      });
                    },
                    activeColor: colorScheme.primary,
                  ),
                  const Expanded(
                    child: Text(
                      'Stop after Current Deal Ends',
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Build conditional take profit content for advanced bot
  Widget _buildTakeProfitContent(ThemeData theme) {
    switch (selectedTakeProfitType) {
      case 'Absolute Take Profit':
        return _buildAbsoluteTakeProfitSection(theme);
      case 'Fixed Trailing Take Profit':
        return _buildFixedTrailingTakeProfitSection(theme);
      case 'Variable Trailing Take Profit':
        return _buildVariableTrailingTakeProfitSection(theme);
      default:
        return _buildAbsoluteTakeProfitSection(theme);
    }
  }

  // Build Absolute Take Profit section (same as current slider)
  Widget _buildAbsoluteTakeProfitSection(ThemeData theme) {
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(20),
      color: ThemeReflector.surfaceColor(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'TAKE PROFIT',
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.secondary,
              ),
              fontSize: 12,
              fontWeight: FontWeight.w600,
              letterSpacing: 1.2,
            ),
          ),
          const SizedBox(height: 16),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              trackHeight: 6,
              activeTrackColor: colorScheme.primary,
              inactiveTrackColor: ThemeReflector.surfaceColor(context),
              thumbColor: theme.scaffoldBackgroundColor,
              thumbShape: const RoundSliderThumbShape(
                enabledThumbRadius: 8,
                elevation: 4,
              ),
              overlayColor: colorScheme.primary,
            ),
            child: Slider(
              value: takeProfit,
              onChanged: (value) {
                setState(() {
                  takeProfit = value;
                });
              },
              min: 0.1,
              max: 50.0,
              divisions: 499,
            ),
          ),
          Text(
            '${takeProfit.toStringAsFixed(1)}%',
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.primary,
              ),
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // Build Fixed Trailing Take Profit section
  Widget _buildFixedTrailingTakeProfitSection(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(20),
      color: ThemeReflector.surfaceColor(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'FIXED TRAILING TAKE PROFIT',
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.secondary,
              ),
              fontSize: 12,
              fontWeight: FontWeight.w600,
              letterSpacing: 1.2,
            ),
          ),
          const SizedBox(height: 16),
          // "Sell ___% of asset at every ___% above Buy Price"
          Row(
            children: [
              const Text('Sell '),
              Container(
                width: 60,
                height: 36,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: const Color(0xFFD1D5DB),
                    width: 1.0,
                  ),
                  borderRadius: BorderRadius.circular(6),
                  color: Colors.white,
                ),
                child: TextField(
                  controller: _fixedTrailingSellPercentController,
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 14),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                  ),
                ),
              ),
              const Text('% of asset at every '),
              Container(
                width: 60,
                height: 36,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: const Color(0xFFD1D5DB),
                    width: 1.0,
                  ),
                  borderRadius: BorderRadius.circular(6),
                  color: Colors.white,
                ),
                child: TextField(
                  controller: _fixedTrailingPricePercentController,
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 14),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                  ),
                ),
              ),
              const Text('% above Buy Price'),
            ],
          ),
        ],
      ),
    );
  }

  // Build Variable Trailing Take Profit section
  Widget _buildVariableTrailingTakeProfitSection(ThemeData theme) {
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(20),
      color: ThemeReflector.surfaceColor(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'VARIABLE TRAILING TAKE PROFIT',
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.secondary,
              ),
              fontSize: 12,
              fontWeight: FontWeight.w600,
              letterSpacing: 1.2,
            ),
          ),
          const SizedBox(height: 16),
          // Dynamic list of variable trailing entries
          ...variableTrailingControllers.asMap().entries.map((entry) {
            int index = entry.key;
            Map<String, TextEditingController> controllers = entry.value;

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Text('${index + 1}. Sell '),
                  Container(
                    width: 60,
                    height: 36,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: const Color(0xFFD1D5DB),
                        width: 1.0,
                      ),
                      borderRadius: BorderRadius.circular(6),
                      color: Colors.white,
                    ),
                    child: TextField(
                      controller: controllers['sellPercent']!,
                      keyboardType: TextInputType.number,
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 14),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                      ),
                    ),
                  ),
                  const Text('% of asset when price hits '),
                  Container(
                    width: 60,
                    height: 36,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: const Color(0xFFD1D5DB),
                        width: 1.0,
                      ),
                      borderRadius: BorderRadius.circular(6),
                      color: Colors.white,
                    ),
                    child: TextField(
                      controller: controllers['pricePercent']!,
                      keyboardType: TextInputType.number,
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 14),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                      ),
                    ),
                  ),
                  const Text('% above Buy Price'),
                  if (index > 0) // Can delete all except first entry
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () {
                        setState(() {
                          // Dispose controllers before removing
                          controllers['sellPercent']!.dispose();
                          controllers['pricePercent']!.dispose();
                          variableTrailingControllers.removeAt(index);
                        });
                      },
                    ),
                ],
              ),
            );
          }).toList(),
          const SizedBox(height: 12),
          // Add/Remove buttons
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    variableTrailingControllers.add({
                      'sellPercent': TextEditingController(),
                      'pricePercent': TextEditingController(),
                    });
                  });
                },
                icon: const Icon(Icons.add, size: 16),
                label: const Text('Add'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  minimumSize: const Size(0, 32),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  textStyle: const TextStyle(fontSize: 12),
                ),
              ),
              const SizedBox(width: 8),
              if (variableTrailingControllers.length > 1)
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      // Dispose controllers before removing
                      final lastControllers = variableTrailingControllers.last;
                      lastControllers['sellPercent']?.dispose();
                      lastControllers['pricePercent']?.dispose();
                      variableTrailingControllers.removeLast();
                    });
                  },
                  icon: const Icon(Icons.remove, size: 16),
                  label: const Text('Remove'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: colorScheme.primary,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    minimumSize: const Size(0, 32),
                    side: BorderSide(color: colorScheme.primary, width: 1),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                    textStyle: const TextStyle(fontSize: 12),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  // Build conditional stop loss content for advanced bot
  Widget _buildStopLossContent(ThemeData theme) {
    switch (selectedStopLossType) {
      case 'Absolute':
        return _buildAbsoluteStopLossSection(theme);
      case 'Fixed Trailing Stoploss':
        return _buildFixedTrailingStopLossSection(theme);
      case 'Variable Trailing Stoploss':
        return _buildVariableTrailingStopLossSection(theme);
      default:
        return _buildAbsoluteStopLossSection(theme);
    }
  }

  // Build Absolute Stop Loss section (same as current slider)
  Widget _buildAbsoluteStopLossSection(ThemeData theme) {
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(20),
      color: ThemeReflector.surfaceColor(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'STOP LOSS',
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.secondary,
              ),
              fontSize: 12,
              fontWeight: FontWeight.w600,
              letterSpacing: 1.2,
            ),
          ),
          const SizedBox(height: 16),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              trackHeight: 6,
              activeTrackColor: colorScheme.primary,
              inactiveTrackColor: ThemeReflector.surfaceColor(context),
              thumbColor: theme.scaffoldBackgroundColor,
              thumbShape: const RoundSliderThumbShape(
                enabledThumbRadius: 8,
                elevation: 4,
              ),
              overlayColor: colorScheme.primary,
            ),
            child: Slider(
              value: stopLoss,
              onChanged: (value) {
                setState(() {
                  stopLoss = value;
                });
              },
              min: 0.1,
              max: 50.0,
              divisions: 499,
            ),
          ),
          Text(
            '${stopLoss.toStringAsFixed(1)}%',
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.primary,
              ),
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // Build Fixed Trailing Stop Loss section
  Widget _buildFixedTrailingStopLossSection(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(20),
      color: ThemeReflector.surfaceColor(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'FIXED TRAILING STOP LOSS',
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.secondary,
              ),
              fontSize: 12,
              fontWeight: FontWeight.w600,
              letterSpacing: 1.2,
            ),
          ),
          const SizedBox(height: 16),
          // Similar to take profit but for stop loss
          Row(
            children: [
              const Text('Stop loss '),
              Container(
                width: 60,
                height: 36,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: const Color(0xFFD1D5DB),
                    width: 1.0,
                  ),
                  borderRadius: BorderRadius.circular(6),
                  color: Colors.white,
                ),
                child: const TextField(
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                  ),
                ),
              ),
              const Text('% at every '),
              Container(
                width: 60,
                height: 36,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: const Color(0xFFD1D5DB),
                    width: 1.0,
                  ),
                  borderRadius: BorderRadius.circular(6),
                  color: Colors.white,
                ),
                child: const TextField(
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                  ),
                ),
              ),
              const Text('% below Buy Price'),
            ],
          ),
        ],
      ),
    );
  }

  // Build Variable Trailing Stop Loss section
  Widget _buildVariableTrailingStopLossSection(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(20),
      color: ThemeReflector.surfaceColor(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'VARIABLE TRAILING STOP LOSS',
            style: TextStyle(
              color: ThemeReflector.textColor(
                context,
                importance: TextImportance.secondary,
              ),
              fontSize: 12,
              fontWeight: FontWeight.w600,
              letterSpacing: 1.2,
            ),
          ),
          const SizedBox(height: 16),
          // Similar structure to variable trailing take profit
          const Text(
            '1. Stop loss 25% when price drops 5% below Buy Price',
            style: TextStyle(fontSize: 14),
          ),
          const SizedBox(height: 8),
          const Text(
            '2. Stop loss 50% when price drops 10% below Buy Price',
            style: TextStyle(fontSize: 14),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  // Add stop loss level logic
                },
                icon: const Icon(Icons.add, size: 16),
                label: const Text('Add'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  minimumSize: const Size(0, 32),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  textStyle: const TextStyle(fontSize: 12),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Build Advanced-specific Exit UI
  Widget _buildAdvancedExitSection(ThemeData theme) {
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Take Profit Type Selector
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'TAKE PROFIT TYPE',
                    style: TextStyle(
                      color: ThemeReflector.textColor(
                        context,
                        importance: TextImportance.secondary,
                      ),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    Icons.help_outline,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: const Color(0xFFD1D5DB),
                    width: 1.0,
                  ),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: selectedTakeProfitType,
                    isExpanded: true,
                    icon: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                    style: TextStyle(
                      color: ThemeReflector.textColor(
                        context,
                        importance: TextImportance.primary,
                      ),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    items: [
                      'Absolute Take Profit',
                      'Fixed Trailing Take Profit',
                      'Variable Trailing Take Profit'
                    ].map((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(
                          value,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: ThemeReflector.textColor(
                              context,
                              importance: TextImportance.primary,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        setState(() {
                          selectedTakeProfitType = newValue;
                        });
                      }
                    },
                  ),
                ),
              ),
            ],
          ),
        ),

        // Conditional UI based on selection
        _buildTakeProfitContent(theme),

        // Stop Loss Type Selector
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'STOP LOSS TYPE',
                    style: TextStyle(
                      color: ThemeReflector.textColor(
                        context,
                        importance: TextImportance.secondary,
                      ),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    Icons.help_outline,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: const Color(0xFFD1D5DB),
                    width: 1.0,
                  ),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: selectedStopLossType,
                    isExpanded: true,
                    icon: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                    style: TextStyle(
                      color: ThemeReflector.textColor(
                        context,
                        importance: TextImportance.primary,
                      ),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    items: [
                      'Absolute',
                      'Fixed Trailing Stoploss',
                      'Variable Trailing Stoploss'
                    ].map((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(
                          value,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: ThemeReflector.textColor(
                              context,
                              importance: TextImportance.primary,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        setState(() {
                          selectedStopLossType = newValue;
                        });
                      }
                    },
                  ),
                ),
              ),
            ],
          ),
        ),

        // Conditional Stop Loss UI based on selection
        _buildStopLossContent(theme),

        // Trading View Section
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  const Text(
                    'Trading view',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    Icons.help_outline,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                ],
              ),
              Switch(
                value: tradingViewEnabled,
                onChanged: (value) {
                  setState(() {
                    tradingViewEnabled = value;
                  });
                },
                activeColor: colorScheme.primary,
              ),
            ],
          ),
        ),

        // Add Indicator Section
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'INDICATORS',
                    style: TextStyle(
                      color: ThemeReflector.textColor(
                        context,
                        importance: TextImportance.secondary,
                      ),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: () {
                      _showAddIndicatorDialog();
                    },
                    icon: Icon(
                      Icons.add,
                      color: theme.colorScheme.primary,
                      size: 16,
                    ),
                    label: Text(
                      'Add Indicator',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          theme.colorScheme.primary.withValues(alpha: 0.1),
                      foregroundColor: theme.colorScheme.primary,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Display added indicators
              indicators.isEmpty
                  ? Padding(
                      padding: const EdgeInsets.symmetric(vertical: 24.0),
                      child: Text(
                        'No indicators added yet. Click "Add Indicator" to add one.',
                        style: TextStyle(
                          color: ThemeReflector.textColor(
                            context,
                            importance: TextImportance.tertiary,
                          ),
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    )
                  : Column(
                      children: indicators
                          .map((indicator) => _buildIndicatorCard(
                              indicator, theme, theme.textTheme))
                          .toList(),
                    ),
            ],
          ),
        ),

        // Minimum Profit for Indicator Trigger Section
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'MINIMUM PROFIT FOR INDICATOR TRIGGER',
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.secondary,
                  ),
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 1.2,
                ),
              ),
              const SizedBox(height: 16),
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  trackHeight: 6,
                  activeTrackColor: ThemeReflector.statusColor(context,
                      status: StatusType.success),
                  inactiveTrackColor: ThemeReflector.surfaceColor(context),
                  thumbColor: theme.scaffoldBackgroundColor,
                  thumbShape: const RoundSliderThumbShape(
                    enabledThumbRadius: 8,
                    elevation: 4,
                  ),
                  overlayColor: ThemeReflector.statusColor(context,
                          status: StatusType.success)
                      .withValues(alpha: 0.2),
                ),
                child: Slider(
                  value: minimumProfitForTrigger,
                  onChanged: (value) {
                    setState(() {
                      minimumProfitForTrigger = value;
                    });
                  },
                  min: 0.0,
                  max: 100.0,
                  divisions: 1000,
                ),
              ),
              Text(
                '${minimumProfitForTrigger.toStringAsFixed(1)}%',
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.primary,
                  ),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),

        // Disable Bot Settings Section
        Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(20),
          color: ThemeReflector.surfaceColor(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'DISABLE BOT SETTINGS',
                style: TextStyle(
                  color: ThemeReflector.textColor(
                    context,
                    importance: TextImportance.secondary,
                  ),
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 1.2,
                ),
              ),
              const SizedBox(height: 16),
              // Disable Bot if stop loss is hit X in a row
              Row(
                children: [
                  const Text(
                    'Disable Bot if stop loss is hit ',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Container(
                    width: 60,
                    height: 40,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: const Color(0xFFD1D5DB),
                        width: 1.0,
                      ),
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.white,
                    ),
                    child: TextField(
                      keyboardType: TextInputType.number,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      onChanged: (value) {
                        setState(() {
                          stopLossRowCount = int.tryParse(value) ?? 0;
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'in a row',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Stop Bot After Current Deal Ends
              Row(
                children: [
                  Checkbox(
                    value: stopAfterCurrentDeal,
                    onChanged: (value) {
                      setState(() {
                        stopAfterCurrentDeal = value ?? false;
                      });
                    },
                    activeColor: theme.colorScheme.primary,
                  ),
                  const Expanded(
                    child: Text(
                      'Stop Bot After Current Deal Ends',
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Auto-close open positions at end of trading day
              Row(
                children: [
                  Checkbox(
                    value: autoCloseAtEndOfDay,
                    onChanged: (value) {
                      setState(() {
                        autoCloseAtEndOfDay = value ?? false;
                      });
                    },
                    activeColor: theme.colorScheme.primary,
                  ),
                  const Expanded(
                    child: Text(
                      'Auto-close open positions at end of trading day',
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    // Dispose basic controllers
    _sellPriceController.dispose();
    _sellPriceForSellBotController.dispose();
    _sellPriceLowerController.dispose();
    _minProfitController.dispose();

    // Dispose advanced bot controllers
    _fixedTrailingSellPercentController.dispose();
    _fixedTrailingPricePercentController.dispose();

    // Dispose variable trailing controllers
    for (var controllerMap in variableTrailingControllers) {
      controllerMap['sellPercent']?.dispose();
      controllerMap['pricePercent']?.dispose();
    }

    super.dispose();
  }
}
